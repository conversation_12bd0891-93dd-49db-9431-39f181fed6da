
@{
    ViewBag.Title = "AddCar";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@model CarsList.Models.CarsModel

<div class="row">
    <div class="col-md-6">
        <h2>Add a Car</h2>
        @using (Html.BeginForm())
        {    
            <div class="form-group">
                <label>Car ID:</label>
                @Html.TextBoxFor(x=>x.CarID, new { @class = "form-control" })
            </div>

            <div class="form-group">
                <label>Car Brand:</label>
                @Html.TextBoxFor(x=>x.<PERSON>, new { @class = "form-control" })
            </div>

            <div class="form-group">
                <label>Car Model:</label>
                @Html.TextBoxFor(x=>x.CarModel, new { @class = "form-control" })
            </div>

            <div class="form-group">
                <label>Car Description:</label>
                @Html.TextBoxFor(x=>x.CarDescription, new { @class = "form-control" })
            </div>

            <div class="form-group">
                <label>Car Price (PHP):</label>
                @Html.TextBoxFor(x=>x.CarPrice, new { @class = "form-control" })
            </div>

            <input type="submit" value="Save!" class="btn btn-primary" />
            <input type="button" value="Cancel" class="btn btn-secondary" />
        }
    </div>
    
    <div class="col-md-6">
        <h2>Car Details</h2>
        <div class="card">
            <div class="card-body">
                <p><strong>Car ID:</strong> @ViewBag.CarID</p>
                <p><strong>Car Brand:</strong> @ViewBag.CarBrand</p>
                <p><strong>Car Model:</strong> @ViewBag.CarModel</p>
                <p><strong>Car Description:</strong> @ViewBag.CarDescription</p>
                <p><strong>Car Price (PHP):</strong> @ViewBag.CarPrice</p>
            </div>
        </div>
    </div>
</div>
