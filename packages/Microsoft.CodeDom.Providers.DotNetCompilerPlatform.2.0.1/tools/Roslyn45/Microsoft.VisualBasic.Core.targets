﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target
          Name="CoreCompile"
          Inputs="$(MSBuildAllProjects);
                @(Compile);
                @(_CoreCompileResourceInputs);
                $(ApplicationIcon);
                $(AssemblyOriginatorKeyFile);
                @(ReferencePath);
                @(CompiledLicenseFile);
                @(LinkResource);
                @(EmbeddedDocumentation);
                $(Win32Resource);
                $(Win32Manifest);
                @(CustomAdditionalCompileInputs);
                $(ResolvedCodeAnalysisRuleSet)"
          Outputs="@(DocFileItem);
                 @(IntermediateAssembly);
                 @(_DebugSymbolsIntermediatePath);
                 $(NonExistentFile);
                 @(CustomAdditionalCompileOutputs)"
          Returns="@(VbcCommandLineArgs)"
          DependsOnTargets="$(CoreCompileDependsOn)"
    >
    <PropertyGroup>
      <_NoWarnings Condition=" '$(WarningLevel)' == '0' ">true</_NoWarnings>
      <_NoWarnings Condition=" '$(WarningLevel)' == '1' ">false</_NoWarnings>
    </PropertyGroup>

    <PropertyGroup>
      <!-- If we are targeting winmdobj we want to specifically the pdbFile property since we do not want it to collide with the output of winmdexp-->
      <PdbFile Condition="'$(PdbFile)' == '' and '$(OutputType)' == 'winmdobj' and '$(DebugSymbols)' == 'true'">$(IntermediateOutputPath)$(TargetName).compile.pdb</PdbFile>
    </PropertyGroup>

    <ItemGroup Condition="'$(TargetingClr2Framework)'=='true'">
      <ReferencePath>
        <EmbedInteropTypes/>
      </ReferencePath>
    </ItemGroup>

    <!-- Prefer32Bit was introduced in .NET 4.5. Set it to false if we are targeting 4.0 -->
    <PropertyGroup Condition="('$(TargetFrameworkVersion)' == 'v4.0')">
      <Prefer32Bit>false</Prefer32Bit>
    </PropertyGroup>

    <ItemGroup Condition="('$(AdditionalFileItemNames)' != '')">
      <AdditionalFileItems Include="$(AdditionalFileItemNames)" />
      <AdditionalFiles Include="@(%(AdditionalFileItems.Identity))" />
    </ItemGroup>

    <PropertyGroup Condition="'$(UseSharedCompilation)' == ''">
      <UseSharedCompilation>true</UseSharedCompilation>
    </PropertyGroup>

    <!-- Condition is to filter out the _CoreCompileResourceInputs so that it doesn't pass in culture resources to the compiler -->
    <Vbc  Condition=" '%(_CoreCompileResourceInputs.WithCulture)' != 'true' "
          AdditionalLibPaths="$(AdditionalLibPaths)"
          AddModules="@(AddModules)"
          AdditionalFiles="@(AdditionalFiles)"
          Analyzers="@(Analyzer)"
          BaseAddress="$(BaseAddress)"
          ChecksumAlgorithm="$(ChecksumAlgorithm)"
          CodeAnalysisRuleSet="$(ResolvedCodeAnalysisRuleSet)"
          CodePage="$(CodePage)"
          DebugType="$(DebugType)"
          DefineConstants="$(FinalDefineConstants)"
          DelaySign="$(DelaySign)"
          DisabledWarnings="$(NoWarn)"
          DocumentationFile="@(DocFileItem)"
          EmitDebugInformation="$(DebugSymbols)"
          EnvironmentVariables="$(VbcEnvironment)"
          ErrorLog="$(ErrorLog)"
          ErrorReport="$(ErrorReport)"
          Features="$(Features)"
          FileAlignment="$(FileAlignment)"
          GenerateDocumentation="$(GenerateDocumentation)"
          HighEntropyVA="$(HighEntropyVA)"
          Imports="@(Import)"
          KeyContainer="$(KeyContainerName)"
          KeyFile="$(KeyOriginatorFile)"
          LangVersion="$(LangVersion)"
          LinkResources="@(LinkResource)"
          MainEntryPoint="$(StartupObject)"
          ModuleAssemblyName="$(ModuleAssemblyName)"
          NoConfig="true"
          NoStandardLib="$(NoCompilerStandardLib)"
          NoVBRuntimeReference="$(NoVBRuntimeReference)"
          NoWarnings="$(_NoWarnings)"
          NoWin32Manifest="$(NoWin32Manifest)"
          Optimize="$(Optimize)"
          Deterministic="$(Deterministic)"
          PublicSign="$(PublicSign)"
          OptionCompare="$(OptionCompare)"
          OptionExplicit="$(OptionExplicit)"
          OptionInfer="$(OptionInfer)"
          OptionStrict="$(OptionStrict)"
          OptionStrictType="$(OptionStrictType)"
          OutputAssembly="@(IntermediateAssembly)"
          PdbFile="$(PdbFile)"
          Platform="$(PlatformTarget)"
          Prefer32Bit="$(Prefer32Bit)"
          PreferredUILang="$(PreferredUILang)"
          ProvideCommandLineArgs="$(ProvideCommandLineArgs)"
          References="@(ReferencePath)"
          RemoveIntegerChecks="$(RemoveIntegerChecks)"
          ReportAnalyzer="$(ReportAnalyzer)"
          Resources="@(_CoreCompileResourceInputs);@(CompiledLicenseFile)"
          ResponseFiles="$(CompilerResponseFile)"
          RootNamespace="$(RootNamespace)"
          RuntimeMetadataVersion="$(RuntimeMetadataVersion)"
          SdkPath="$(FrameworkPathOverride)"
          SkipCompilerExecution="$(SkipCompilerExecution)"
          Sources="@(Compile)"
          SubsystemVersion="$(SubsystemVersion)"
          TargetCompactFramework="$(TargetCompactFramework)"
          TargetType="$(OutputType)"
          ToolExe="$(VbcToolExe)"
          ToolPath="$(VbcToolPath)"
          TreatWarningsAsErrors="$(TreatWarningsAsErrors)"
          UseHostCompilerIfAvailable="$(UseHostCompilerIfAvailable)"
          UseSharedCompilation="$(UseSharedCompilation)"
          Utf8Output="$(Utf8Output)"
          VBRuntimePath="$(VBRuntimePath)"
          Verbosity="$(VbcVerbosity)"
          VsSessionGuid="$(VsSessionGuid)"
          WarningsAsErrors="$(WarningsAsErrors)"
          WarningsNotAsErrors="$(WarningsNotAsErrors)"
          Win32Icon="$(ApplicationIcon)"
          Win32Manifest="$(Win32Manifest)"
          Win32Resource="$(Win32Resource)"
          VBRuntime="$(VBRuntime)"
          PathMap="$(PathMap)"
          >
      <Output TaskParameter="CommandLineArgs" ItemName="VbcCommandLineArgs" />
    </Vbc>
    <ItemGroup>
      <_CoreCompileResourceInputs Remove="@(_CoreCompileResourceInputs)" />
    </ItemGroup>

    <CallTarget Targets="$(TargetsTriggeredByCompilation)" Condition="'$(TargetsTriggeredByCompilation)' != ''"/>
  </Target>
</Project>
