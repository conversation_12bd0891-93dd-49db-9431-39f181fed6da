﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c)  Microsoft.  All Rights Reserved.  Licensed under the Apache License, Version 2.0.  See License.txt in the project root for license information. -->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="Microsoft.Managed.Core.targets"/>

  <Target Name="CoreCompile"
          Inputs="$(MSBuildAllProjects);
                  @(Compile);
                  @(_CoreCompileResourceInputs);
                  $(ApplicationIcon);
                  $(AssemblyOriginatorKeyFile);
                  @(ReferencePathWithRefAssemblies);
                  @(CompiledLicenseFile);
                  @(LinkResource);
                  @(EmbeddedDocumentation);
                  $(Win32Resource);
                  $(Win32Manifest);
                  @(CustomAdditionalCompileInputs);
                  $(ResolvedCodeAnalysisRuleSet);
                  @(AdditionalFiles);
                  @(EmbeddedFiles)"
          Outputs="@(DocFileItem);
                   @(IntermediateAssembly);
                   @(IntermediateRefAssembly);
                   @(_DebugSymbolsIntermediatePath);
                   $(NonExistentFile);
                   @(CustomAdditionalCompileOutputs)"
          Returns="@(CscCommandLineArgs)"
          DependsOnTargets="$(CoreCompileDependsOn);_BeforeVBCSCoreCompile">
    <!-- These two compiler warnings are raised when a reference is bound to a different version
             than specified in the assembly reference version number.  MSBuild raises the same warning in this case,
             so the compiler warning would be redundant. -->
    <PropertyGroup Condition="('$(TargetFrameworkVersion)' != 'v1.0') and ('$(TargetFrameworkVersion)' != 'v1.1')">
      <NoWarn>$(NoWarn);1701;1702</NoWarn>
    </PropertyGroup>

    <PropertyGroup>
      <!-- To match historical behavior, when inside VS11+ disable the warning from csc.exe indicating that no sources were passed in-->
      <NoWarn Condition="'$(BuildingInsideVisualStudio)' == 'true' AND '$(VisualStudioVersion)' != '' AND '$(VisualStudioVersion)' &gt; '10.0'">$(NoWarn);2008</NoWarn>
    </PropertyGroup>

    <PropertyGroup>
      <!-- If the user has specified AppConfigForCompiler, we'll use it. If they have not, but they set UseAppConfigForCompiler,
                 then we'll use AppConfig -->
      <AppConfigForCompiler Condition="'$(AppConfigForCompiler)' == '' AND '$(UseAppConfigForCompiler)' == 'true'">$(AppConfig)</AppConfigForCompiler>

      <!-- If we are targeting winmdobj we want to specifically the pdbFile property since we do not want it to collide with the output of winmdexp-->
      <PdbFile Condition="'$(PdbFile)' == '' AND '$(OutputType)' == 'winmdobj' AND '$(_DebugSymbolsProduced)' == 'true'">$(IntermediateOutputPath)$(TargetName).compile.pdb</PdbFile>
    </PropertyGroup>

    <!-- Condition is to filter out the _CoreCompileResourceInputs so that it doesn't pass in culture resources to the compiler -->
    <Csc Condition="'%(_CoreCompileResourceInputs.WithCulture)' != 'true'"
         AdditionalLibPaths="$(AdditionalLibPaths)"
         AddModules="@(AddModules)"
         AdditionalFiles="@(AdditionalFiles)"
         AllowUnsafeBlocks="$(AllowUnsafeBlocks)"
         Analyzers="@(Analyzer)"
         ApplicationConfiguration="$(AppConfigForCompiler)"
         BaseAddress="$(BaseAddress)"
         CheckForOverflowUnderflow="$(CheckForOverflowUnderflow)"
         ChecksumAlgorithm="$(ChecksumAlgorithm)"
         CodeAnalysisRuleSet="$(ResolvedCodeAnalysisRuleSet)"
         CodePage="$(CodePage)"
         DebugType="$(DebugType)"
         DefineConstants="$(DefineConstants)"
         DelaySign="$(DelaySign)"
         DisabledWarnings="$(NoWarn)"
         DocumentationFile="@(DocFileItem)"
         EmbedAllSources="$(EmbedAllSources)"
         EmbeddedFiles="@(EmbeddedFiles)"
         EmitDebugInformation="$(DebugSymbols)"
         EnvironmentVariables="$(CscEnvironment)"
         ErrorEndLocation="$(ErrorEndLocation)"
         ErrorLog="$(ErrorLog)"
         ErrorReport="$(ErrorReport)"
         Features="$(Features)"
         FileAlignment="$(FileAlignment)"
         GenerateFullPaths="$(GenerateFullPaths)"
         HighEntropyVA="$(HighEntropyVA)"
         Instrument="$(Instrument)"
         KeyContainer="$(KeyContainerName)"
         KeyFile="$(KeyOriginatorFile)"
         LangVersion="$(LangVersion)"
         LinkResources="@(LinkResource)"
         MainEntryPoint="$(StartupObject)"
         ModuleAssemblyName="$(ModuleAssemblyName)"
         NoConfig="true"
         NoLogo="$(NoLogo)"
         NoStandardLib="$(NoCompilerStandardLib)"
         NoWin32Manifest="$(NoWin32Manifest)"
         Optimize="$(Optimize)"
         Deterministic="$(Deterministic)"
         PublicSign="$(PublicSign)"
         OutputAssembly="@(IntermediateAssembly)"
         OutputRefAssembly="@(IntermediateRefAssembly)"
         PdbFile="$(PdbFile)"
         Platform="$(PlatformTarget)"
         Prefer32Bit="$(Prefer32Bit)"
         PreferredUILang="$(PreferredUILang)"
         ProvideCommandLineArgs="$(ProvideCommandLineArgs)"
         References="@(ReferencePathWithRefAssemblies)"
         ReportAnalyzer="$(ReportAnalyzer)"
         Resources="@(_CoreCompileResourceInputs);@(CompiledLicenseFile)"
         ResponseFiles="$(CompilerResponseFile)"
         RuntimeMetadataVersion="$(RuntimeMetadataVersion)"
         SharedCompilationId="$(SharedCompilationId)"
         SkipCompilerExecution="$(SkipCompilerExecution)"
         Sources="@(Compile)"
         SubsystemVersion="$(SubsystemVersion)"
         TargetType="$(OutputType)"
         ToolExe="$(CscToolExe)"
         ToolPath="$(CscToolPath)"
         TreatWarningsAsErrors="$(TreatWarningsAsErrors)"
         UseHostCompilerIfAvailable="$(UseHostCompilerIfAvailable)"
         UseSharedCompilation="$(UseSharedCompilation)"
         Utf8Output="$(Utf8Output)"
         VsSessionGuid="$(VsSessionGuid)"
         WarningLevel="$(WarningLevel)"
         WarningsAsErrors="$(WarningsAsErrors)"
         WarningsNotAsErrors="$(WarningsNotAsErrors)"
         Win32Icon="$(ApplicationIcon)"
         Win32Manifest="$(Win32Manifest)"
         Win32Resource="$(Win32Resource)"
         PathMap="$(PathMap)"
         SourceLink="$(SourceLink)">
      <Output TaskParameter="CommandLineArgs" ItemName="CscCommandLineArgs" />
    </Csc>

    <ItemGroup>
      <_CoreCompileResourceInputs Remove="@(_CoreCompileResourceInputs)" />
    </ItemGroup>

    <CallTarget Targets="$(TargetsTriggeredByCompilation)" Condition="'$(TargetsTriggeredByCompilation)' != ''" />
  </Target>
</Project>