
@model List<CarsList.Models.CarsModel>

@{
    ViewBag.Title = "List of Cars";
}

<div class="row">
    <div class="col-md-12">
        <h2>@ViewBag.Cars</h2>

        @if (Model != null && Model.Any())
        {
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Car ID</th>
                        <th>Car Brand</th>
                        <th>Car Model</th>
                        <th>Car Description</th>
                        <th>Car Price (PHP)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var x in Model)
                    {
                        <tr>
                            <td>@x.CarID</td>
                            <td>@x.CarBrand</td>
                            <td>@x.CarModel</td>
                            <td>@x.CarDescription</td>
                            <td>@x.CarPrice.ToString("N0")</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <div class="alert alert-info">
                <h4>No cars found</h4>
                <p>There are currently no cars in the list. @Html.ActionLink("Add the first car", "AddCar", "Cars")!</p>
            </div>
        }
    </div>
</div>

