
@{
    ViewBag.Title = "Index";
}

<h2>@ViewBag.Cars</h2>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Car ID</th>
            <th><PERSON> Brand</th>
            <th>Car Model</th>
            <th>Car Description</th>
            <th>Car Price (PHP)</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var x in Model)
        {
            <tr>
                <td>@x.CarID</td>
                <td>@x.CarBrand</td>
                <td>@x.CarModel</td>
                <td>@x.CarDescription</td>
                <td>@x.CarPrice.ToString("N0")</td>
            </tr>
        }
    </tbody>
</table>

