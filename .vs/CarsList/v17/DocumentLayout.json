{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\CarsList\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|c:\\users\\<USER>\\source\\repos\\carslist\\carslist\\controllers\\carscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|solutionrelative:carslist\\controllers\\carscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|C:\\Users\\<USER>\\source\\repos\\CarsList\\carslist\\models\\carsmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|solutionrelative:carslist\\models\\carsmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|C:\\Users\\<USER>\\source\\repos\\CarsList\\carslist\\views\\cars\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|solutionrelative:carslist\\views\\cars\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|C:\\Users\\<USER>\\source\\repos\\CarsList\\carslist\\views\\cars\\addcar.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A00014F7-9068-4CC2-A43E-BE7083E75C9F}|CarsList\\CarsList.csproj|solutionrelative:carslist\\views\\cars\\addcar.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "AddCar.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Views\\Cars\\AddCar.cshtml", "RelativeDocumentMoniker": "CarsList\\Views\\Cars\\AddCar.cshtml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Views\\Cars\\AddCar.cshtml", "RelativeToolTip": "CarsList\\Views\\Cars\\AddCar.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-24T07:50:37.329Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Index.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Views\\Cars\\Index.cshtml", "RelativeDocumentMoniker": "CarsList\\Views\\Cars\\Index.cshtml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Views\\Cars\\Index.cshtml", "RelativeToolTip": "CarsList\\Views\\Cars\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-24T07:50:18.736Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CarsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Controllers\\CarsController.cs", "RelativeDocumentMoniker": "CarsList\\Controllers\\CarsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Controllers\\CarsController.cs", "RelativeToolTip": "CarsList\\Controllers\\CarsController.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwA8AAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-24T07:49:06.161Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CarsModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Models\\CarsModel.cs", "RelativeDocumentMoniker": "CarsList\\Models\\CarsModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CarsList\\CarsList\\Models\\CarsModel.cs", "RelativeToolTip": "CarsList\\Models\\CarsModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-24T07:48:31.59Z"}]}]}]}