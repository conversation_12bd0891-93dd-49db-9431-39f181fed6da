using CarsList.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace CarsList.Controllers
{
    public class CarsController : Controller
    {
        // GET: Cars
        public ActionResult Index()
        {
            ViewBag.Cars = "List of Cars";
            List<Models.CarsModel> CarList = new List<Models.CarsModel>()
            {
                new CarsModel { CarID = 1, CarBrand = "Toyota", CarModel = "Corolla", CarDescription = "A compact sedan with reliable fuel efficiency.", CarPrice = 850000 },
                new CarsModel { CarID = 2, CarBrand = "Honda", CarModel = "Civic", CarDescription = "Sporty sedan known for its modern design.", CarPrice = 1200000 },
                new CarsModel { CarID = 3, CarBrand = "Ford", CarModel = "Mustang", CarDescription = "Iconic sports car with powerful engine options.", CarPrice = 2500000 },
                new CarsModel { CarID = 4, CarBrand = "Nissan", CarModel = "Altima", CarDescription = "Mid-size sedan with a comfortable ride.", CarPrice = 1400000 },
                new CarsModel { CarID = 5, CarBrand = "BMW", CarModel = "X5", CarDescription = "Luxury SUV with cutting-edge technology.", CarPrice = 3000000 },
                new CarsModel { CarID = 6, CarBrand = "Chevrolet", CarModel = "Silverado", CarDescription = "Full-size pickup truck with a robust build.", CarPrice = 1800000 },
                new CarsModel { CarID = 7, CarBrand = "Mercedes", CarModel = "Benz E-Class", CarDescription = "Elegant sedan with premium features and style.", CarPrice = 2800000 },
                new CarsModel { CarID = 8, CarBrand = "Audi", CarModel = "A4", CarDescription = "Sedan with a blend of performance and luxury.", CarPrice = 2200000 },
                new CarsModel { CarID = 9, CarBrand = "Hyundai", CarModel = "Tucson", CarDescription = "Compact SUV offering excellent value for money.", CarPrice = 1150000 },
                new CarsModel { CarID = 10, CarBrand = "Kia", CarModel = "Sorento", CarDescription = "Versatile SUV with a spacious interior.", CarPrice = 1450000 }
            };

            return View(CarList);
        }

        // GET: Cars/AddCar
        public ActionResult AddCar()
        {
            ViewBag.AddCar = "Add a Car";
            return View();
        }

        // POST: Cars/AddCar
        [HttpPost]
        public ActionResult AddCar(Models.CarsModel c)
        {
            ViewBag.AddCar = "Add a Car";
            ViewBag.CarID = c.CarID;
            ViewBag.CarBrand = c.CarBrand;
            ViewBag.CarModel = c.CarModel;
            ViewBag.CarDescription = c.CarDescription;
            ViewBag.CarPrice = c.CarPrice;
            return View(c);
        }
    }
}
